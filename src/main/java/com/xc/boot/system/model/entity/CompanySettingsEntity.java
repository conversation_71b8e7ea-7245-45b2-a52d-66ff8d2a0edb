package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 商户设置实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "company_settings")
@Schema(description = "商户设置实体")
public class CompanySettingsEntity extends BaseEntity {
    /**
     * 商户ID
     */
    @Schema(description = "商户ID")
    @Column(value = "company_id")
    private Integer companyId;

    /**
     * 图片打印尺寸(json)
     */
    @Schema(description = "图片打印尺寸配置(JSON格式)，结构：{\"ctrl\":\"控制类型(full:控制宽高;height:控制高;width:控制宽)\",\"width\":\"宽度(像素)\",\"height\":\"高度(像素)\"}")
    @Column(value = "image_print_size")
    private String imagePrintSize;

    /**
     * 图片导出尺寸(json)
     */
    @Schema(description = "图片导出尺寸配置(JSON格式)，结构：{\"ctrl\":\"控制类型(full:控制宽高;height:控制高;width:控制宽)\",\"width\":\"宽度(像素)\",\"height\":\"高度(像素)\"}")
    @Column(value = "image_export_size")
    private String imageExportSize;

    /**
     * 货品入库审核是否开启
     */
    @Schema(description = "货品入库审核是否开启")
    @Column(value = "income_audit_enabled")
    private Boolean incomeAuditEnabled;

    /**
     * 货品调拨审核是否开启
     */
    @Schema(description = "货品调拨审核是否开启")
    @Column(value = "transfer_audit_enabled")
    private Boolean transferAuditEnabled;

    /**
     * 采购退审核是否开启
     */
    @Schema(description = "采购退审核是否开启")
    @Column(value = "return_audit_enabled")
    private Boolean returnAuditEnabled;

    /**
     * 赠品入库审核是否开启
     */
    @Schema(description = "赠品入库审核是否开启")
    @Column(value = "gift_income_audit_enabled")
    private Boolean giftIncomeAuditEnabled;

    /**
     * 赠品调拨审核是否开启
     */
    @Schema(description = "赠品调拨审核是否开启")
    @Column(value = "gift_transfer_audit_enabled")
    private Boolean giftTransferAuditEnabled;
} 