package com.xc.boot.modules.pda.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.goods.model.form.GoodsTakeForm;
import com.xc.boot.modules.goods.model.query.GoodsTakePageQuery;
import com.xc.boot.modules.goods.model.query.TakeGoodsPageQuery;
import com.xc.boot.modules.goods.model.vo.GoodsTakePageVo;
import com.xc.boot.modules.goods.service.GoodsTakeService;
import com.xc.boot.modules.pda.model.form.RfidTakeForm;
import com.xc.boot.modules.pda.model.result.PdaPageResult;
import com.xc.boot.modules.pda.model.vo.PdaTakeGoodsPageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "PDA-盘点")
@RequestMapping("/api/pda/take")
public class PdaTakeController {
    private final GoodsTakeService goodsTakeService;
    @Operation(summary = "盘点单分页列表")
    @PostMapping("/page")
    public PdaPageResult<GoodsTakePageVo> page(@Validated @RequestBody GoodsTakePageQuery query) {
        query.setTakeMode(2);
        query.setTakeType(1);
        Page<GoodsTakePageVo> page = goodsTakeService.pageTake(query);
        return PdaPageResult.success(page);
    }

    @Operation(summary = "新增盘点单")
    @PostMapping("/add")
    public Result<?> addTake(@Validated(Create.class) @RequestBody GoodsTakeForm form) {
        form.setTakeMode(2);
        form.setType(1);
        goodsTakeService.addTake(form);
        return Result.success();
    }

    @Operation(summary = "盘点单详情查询(用于查询已盘、盘亏、盘盈等数据)")
    @GetMapping("/receiptDetail")
    public Result<GoodsTakePageVo> page(@RequestParam @Valid @NotNull(message = "盘点单ID不能为空") Long id) {
        GoodsTakePageVo vo = goodsTakeService.detailTake(id);
        return Result.success(vo);
    }

    @Operation(summary = "分页查询盘点货品")
    @PostMapping("/detailPage")
    public PdaPageResult<PdaTakeGoodsPageVo> detailPage(@Validated @RequestBody TakeGoodsPageQuery query) {
        Page<PdaTakeGoodsPageVo> vo = goodsTakeService.pdaGoodsPage(query);
        return PdaPageResult.success(vo);
    }

    @Operation(summary = "重置盘点")
    @GetMapping("/reset")
    public Result<?> reset(@RequestParam @Valid @NotNull(message = "盘点单ID不能为空") Long id) {
        goodsTakeService.reset(id);
        return Result.success();
    }

    @Operation(summary = "rfid盘点")
    @PostMapping
    public Result<?> take(@Validated @RequestBody RfidTakeForm form) {
        goodsTakeService.rfidTake(form);
        return Result.success();
    }

    @Operation(summary = "结束盘点")
    @GetMapping("/finish")
    public Result<?> finish(@Valid @NotNull(message = "盘点单id不能为空") @RequestParam Long takeId) {
        goodsTakeService.finish(takeId);
        return Result.success();
    }
}
