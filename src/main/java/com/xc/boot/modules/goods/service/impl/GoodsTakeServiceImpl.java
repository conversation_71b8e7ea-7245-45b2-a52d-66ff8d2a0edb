package com.xc.boot.modules.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrBuilder;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.PriceColumEnum;
import com.xc.boot.common.util.*;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.*;
import com.xc.boot.modules.goods.model.entity.*;
import com.xc.boot.modules.goods.model.form.GoodsTakeEditForm;
import com.xc.boot.modules.goods.model.form.GoodsTakeForm;
import com.xc.boot.modules.goods.model.query.GoodsTakePageQuery;
import com.xc.boot.modules.goods.model.query.TakeGoodsListQuery;
import com.xc.boot.modules.goods.model.query.TakeGoodsPageQuery;
import com.xc.boot.modules.goods.model.vo.*;
import com.xc.boot.modules.goods.service.GoodsTakeService;
import com.xc.boot.modules.merchant.mapper.SubclassMapper;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.SubclassEntity;
import com.xc.boot.modules.pda.model.form.RfidTakeForm;
import com.xc.boot.modules.pda.model.vo.PdaTakeGoodsPageVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeCategoryTableDef.GOODS_TAKE_CATEGORY;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeCounterTableDef.GOODS_TAKE_COUNTER;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeDetailTableDef.GOODS_TAKE_DETAIL;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeStyleTableDef.GOODS_TAKE_STYLE;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeTableDef.GOODS_TAKE;
import static com.xc.boot.modules.merchant.model.entity.table.SubclassTableDef.SUBCLASS;
import static com.xc.boot.modules.pda.model.entity.table.GoodsHasRfidTableDef.GOODS_HAS_RFID;

@Service
@RequiredArgsConstructor
public class GoodsTakeServiceImpl extends ServiceImpl<GoodsTakeMapper, GoodsTakeEntity> implements GoodsTakeService {
    private final ListFillService listFillService;
    private final SubclassMapper subclassMapper;
    private final GoodsMapper goodsMapper;
    private final GoodsTakeDetailMapper goodsTakeDetailMapper;
    private final GoodsTakeCounterMapper goodsTakeCounterMapper;
    private final GoodsTakeCategoryMapper goodsTakeCategoryMapper;
    private final GoodsTakeStyleMapper goodsTakeStyleMapper;
    private final RedissonClient redissonClient;
    private final RedisTemplate redisTemplate;

    private final static String LOCK_KEY_TAKE_RFID = "take:lock:rfid:";
    private final static String KEY_TAKE_RFID = "take:rfid:";

    @Override
    public Page<GoodsTakePageVo> pageTake(GoodsTakePageQuery query) {
        QueryWrapper wrapper = buildQuery(query);
        wrapper.orderBy(GOODS_TAKE.ID, false);

        if (query.getExport().equals(1)) {
            doExport(wrapper);
            return new Page<>();
        }
        if (query.getPrint().equals(1)) {
            long count = this.count(wrapper);
            query.setPrintNum(count);
        }
        Page<GoodsTakePageVo> page = this.mapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, GoodsTakePageVo.class);
        // 填充列表
        fillList(page.getRecords());
        return page;
    }

    @Override
    @Transactional
    public void addTake(GoodsTakeForm form) {
        Long companyId = SecurityUtils.getCompanyId();
        // 处理盘点范围筛选
        Set<Long> subclassIds = Optional.ofNullable(form.getSubclassIds()).orElse(new HashSet<>());
        Set<Long> categoryIds = Optional.ofNullable(form.getCategoryIds()).orElse(new HashSet<>());
        Set<Long> counterIds = Optional.ofNullable(form.getCounterIds()).orElse(new HashSet<>());
        Set<Long> styleIds = Optional.ofNullable(form.getStyleIds()).orElse(new HashSet<>());
        Map<Long, List<SubclassEntity>> categorySubclassMap = new HashMap<>();

        categoryIds.forEach(id -> categorySubclassMap.put(id, new ArrayList<>()));
        // 根据大类id选择情况，重新处理小类id列表
        if (CollectionUtil.isNotEmpty(form.getSubclassIds())) {
            List<SubclassEntity> subclassEntityList = subclassMapper.selectListByQuery(QueryWrapper.create()
                    .where(SUBCLASS.CATEGORY_ID.in(categoryIds, CollectionUtil.isNotEmpty(categoryIds)))
                    .where(SUBCLASS.ID.in(subclassIds))
                    .where(SUBCLASS.STATUS.eq(1))
                    .where(SUBCLASS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .select(SUBCLASS.ID, SUBCLASS.CATEGORY_ID));
            subclassIds = new HashSet<>();
            for (SubclassEntity subclass : subclassEntityList) {
                List<SubclassEntity> list = categorySubclassMap.getOrDefault(subclass.getCategoryId().longValue(), new ArrayList<>());
                list.add(subclass);
                categorySubclassMap.put(subclass.getCategoryId().longValue(), list);
                subclassIds.add(subclass.getId());
            }
        }

        // 查询符合盘点条件的货品信息
        QueryWrapper queryWrapper = QueryWrapper.create().from(GOODS)
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.MERCHANT_ID.eq(form.getMerchantId()))
                .where(GOODS.SUBCLASS_ID.in(subclassIds, CollectionUtil.isNotEmpty(subclassIds)))
                .where(GOODS.CATEGORY_ID.in(categoryIds, CollectionUtil.isNotEmpty(categoryIds)))
                .where(GOODS.COUNTER_ID.in(counterIds, CollectionUtil.isNotEmpty(counterIds)))
                .where(GOODS.STYLE_ID.in(styleIds, CollectionUtil.isNotEmpty(styleIds)))
                .where(GOODS.STOCK_NUM.add(GOODS.FROZEN_NUM).gt(0));

        // rfid盘点 只盘点已绑定rfid的货品
        if (form.getTakeMode().equals(2)) {
            queryWrapper.where(GOODS.NUM.eq(1))
                    .where(QueryMethods.exists(QueryWrapper.create().from(GOODS_HAS_RFID)
                            .where(GOODS_HAS_RFID.GOODS_ID.eq(GOODS.ID))
                            .where(GOODS_HAS_RFID.RFID.isNotNull())));
        }

        // 保存盘点单
        GoodsTakeEntity goodsTakeEntity = BeanUtil.copyProperties(form, GoodsTakeEntity.class);
        goodsTakeEntity.setStatus(0)
                .setResult(null)
                .setCompleteAt(null)
                .setCompanyId(SecurityUtils.getCompanyId())
                .setTakeCode(SnUtils.generateTakeCode());
        this.mapper.insertSelective(goodsTakeEntity);
        // 保存盘点单筛选范围
        if (CollectionUtil.isNotEmpty(counterIds)) {
            List<GoodsTakeCounterEntity> list = counterIds.stream().map(c -> new GoodsTakeCounterEntity()
                    .setTakeId(goodsTakeEntity.getId())
                    .setCompanyId(companyId)
                    .setCounterId(c)).toList();
            goodsTakeCounterMapper.insertBatchSelective(list);
        }
        if (CollectionUtil.isNotEmpty(styleIds)) {
            List<GoodsTakeStyleEntity> list = styleIds.stream().map(s -> new GoodsTakeStyleEntity()
                    .setTakeId(goodsTakeEntity.getId())
                    .setCompanyId(companyId)
                    .setStyleId(s)).toList();
            goodsTakeStyleMapper.insertBatchSelective(list);
        }
        if (MapUtil.isNotEmpty(categorySubclassMap)) {
            List<GoodsTakeCategoryEntity> list = new ArrayList<>();
            for (Map.Entry<Long, List<SubclassEntity>> entry : categorySubclassMap.entrySet()) {
                if (CollectionUtil.isEmpty(entry.getValue())) {
                    list.add(new GoodsTakeCategoryEntity().setCompanyId(companyId)
                            .setTakeId(goodsTakeEntity.getId())
                            .setCategoryId(entry.getKey()));
                    continue;
                }
                for (SubclassEntity subclass : entry.getValue()) {
                    list.add(new GoodsTakeCategoryEntity().setCompanyId(companyId)
                            .setTakeId(goodsTakeEntity.getId())
                            .setCategoryId(entry.getKey())
                            .setSubclassId(subclass.getId()));
                }
            }
            goodsTakeCategoryMapper.insertBatchSelective(list);
        }
        // 查询货品 并添加悲观锁
        QueryWrapper wrapper = queryWrapper.clone().forUpdate();
        List<GoodsEntity> goodsList = goodsMapper.selectListByQuery(wrapper);
        Assert.notEmpty(goodsList, "此筛选条件下未查询到货品");
        // 循环处理并校验是否存在盘点中货品
        List<GoodsTakeDetailEntity> detailList = new ArrayList<>();
        Set<Long> goodsIds = new HashSet<>();
        int totalNum = 0;
        for (GoodsEntity goods : goodsList) {
            Assert.isTrue(goods.getTakeStatus().equals(0), String.format("货品: %s 正在盘点中，请结束盘点后再试", goods.getGoodsSn()));
            GoodsTakeDetailEntity detail = BeanUtil.copyProperties(goods, GoodsTakeDetailEntity.class);
            detail.setTakeId(goodsTakeEntity.getId())
                    .setTotalNum(goods.getStockNum() + goods.getFrozenNum())
                    .setTakeNum(0)
                    .setAbnormal(0)
                    .setGoodsId(goods.getId())
                    .setId(null);
            goodsIds.add(goods.getId());
            detailList.add(detail);
            totalNum += detail.getTotalNum();
            goods.setTakeStatus(1);
        }
        ListFillUtil.of(detailList)
                .build(listFillService::getRfidByGoodsId, goodsIds, "goodsId", "rfid")
                .handle();
        goodsTakeEntity.setTotalNum(totalNum);
        // 更新盘点单
        this.mapper.update(goodsTakeEntity);
        // 保存盘点单详情
        goodsTakeDetailMapper.insertBatchSelective(detailList, 2000);
        // 设为盘点中
        UpdateChain.of(GoodsEntity.class)
                .where(GOODS.ID.in(goodsIds))
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .set(GOODS.TAKE_STATUS, 1)
                .update();
        OpLogUtils.appendOpLog("货品管理-创建盘点单", "创建盘点单", goodsTakeEntity);
    }

    @Override
    public Page<TakeGoodsVo> goodsList(TakeGoodsListQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .leftJoin(GOODS).on(GOODS.ID.eq(GOODS_TAKE_DETAIL.GOODS_ID))
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(query.getTakeId()))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .select(GOODS_TAKE_DETAIL.ALL_COLUMNS,
                        GOODS.ALL_COLUMNS,
                        GOODS_TAKE_DETAIL.ID.as("id"),
                        GOODS.ID.as("goodsId"));
        if (query.getType().equals(1)) {
            wrapper.where(GOODS_TAKE_DETAIL.GOODS_SN.eq(query.getKeyword()));
        }else {
            wrapper.where(GOODS_TAKE_DETAIL.NAME.like(query.getKeyword()));
        }
        Page<TakeGoodsVo> page = goodsTakeDetailMapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, TakeGoodsVo.class);

        fillTakeGoodsList(page.getRecords());
        return page;
    }

    @Override
    @Transactional
    public void confirm(GoodsTakeEditForm form) {
        GoodsTakeEntity take = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE.ID.eq(form.getTakeId()))
                .where(GOODS_TAKE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .forUpdate());
        Assert.notNull(take, "盘点单不存在");
        Assert.isTrue(take.getStatus().equals(0), "盘点单已结束");
        Assert.isTrue(SecurityUtils.isMain() || SecurityUtils.getMerchantIds().contains(take.getMerchantId()), "无权操作该盘点单");
        GoodsTakeDetailEntity detail = goodsTakeDetailMapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE_DETAIL.ID.eq(form.getId()))
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(take.getId()))
                .forUpdate());
        Assert.notNull(detail, "盘点货品不存在");
        Integer oldTake = detail.getTakeNum();
        Integer newTake = detail.getTakeNum() + form.getNum();
        detail.setTakeNum(newTake);
        detail.setTakeBy(SecurityUtils.getUserId());
        detail.setTakeAt(new Date());
        goodsTakeDetailMapper.update(detail);
        // 更新盘点单数量
        updateTakeNum(take, oldTake, newTake, detail.getTotalNum());
        this.mapper.update(take);
        OpLogUtils.appendOpLog("货品管理-货品盘点", String.format("""
                货品sn: %s
                已盘数量: %d
                盘点数量: %d""", detail.getGoodsSn(), oldTake, newTake), detail.getId());
    }

    @Override
    @Transactional
    public void editNum(GoodsTakeEditForm form) {
        GoodsTakeEntity take = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE.ID.eq(form.getTakeId()))
                .where(GOODS_TAKE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .forUpdate());
        Assert.notNull(take, "盘点单不存在");
        Assert.isTrue(take.getStatus().equals(0), "盘点单已结束");
        Assert.isTrue(SecurityUtils.isMain() || SecurityUtils.getMerchantIds().contains(take.getMerchantId()), "无权操作该盘点单");
        GoodsTakeDetailEntity detail = goodsTakeDetailMapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE_DETAIL.ID.eq(form.getId()))
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(take.getId()))
                .forUpdate());
        Assert.notNull(detail, "盘点货品不存在");
        // 数量相同直接返回
        if (detail.getTakeNum().equals(form.getTakeNum())) {
            return;
        }
        Integer oldTake = detail.getTakeNum();
        detail.setTakeNum(form.getTakeNum());
        detail.setTakeBy(SecurityUtils.getUserId());
        detail.setTakeAt(new Date());
        // 更新明细
        goodsTakeDetailMapper.update(detail);
        // 更新盘点单数量
        updateTakeNum(take, oldTake, form.getTakeNum(), detail.getTotalNum());
        this.mapper.update(take);
        OpLogUtils.appendOpLog("货品管理-修改已盘点数量", String.format("""
            货品sn: %s,
            原盘点数量: %d,
            新盘点数量: %d""", detail.getGoodsSn(), oldTake, form.getTakeNum()), detail.getId());
    }

    /**
     * 修改盘点数量
     * @param take 盘点单
     * @param oldTake 详情原盘点数量
     * @param newTake 详情新盘点数量
     * @param totalNum 详情应盘数量
     */
    private void updateTakeNum(GoodsTakeEntity take, Integer oldTake, Integer newTake, Integer totalNum) {
        if (Objects.equals(oldTake, newTake)) {
            return;
        }
        take.setTakeNum(take.getTakeNum() + newTake - oldTake);
        // 设为未盘点
        if (newTake == 0) {
            int change = oldTake - totalNum;
            if (change > 0) {
                take.setSurplusNum(take.getSurplusNum() - change);
            }else {
                take.setLossNum(take.getLossNum() + change);
            }
            return;
        }
        // 如果盘点过，还原盘点数量
        if (oldTake != 0) {
            int oldChange = oldTake - totalNum;
            if (oldChange > 0) {
                take.setSurplusNum(take.getSurplusNum() - oldChange);
            }else {
                take.setLossNum(take.getLossNum() + oldChange);
            }
        }
        // 维护新的盘点数量
        int newChange = newTake - totalNum;
        if (newChange > 0) {
            take.setSurplusNum(take.getSurplusNum() + newChange);
        }else {
            take.setLossNum(take.getLossNum() - newChange);
        }
    }

    @Override
    public GoodsTakePageVo detailTake(Long id) {
        GoodsTakePageVo take = this.mapper.selectOneByQueryAs(QueryWrapper.create()
                .where(GOODS_TAKE.ID.eq(id))
                .where(GOODS_TAKE.COMPANY_ID.eq(SecurityUtils.getCompanyId())), GoodsTakePageVo.class);
        Assert.notNull(take, "盘点单不存在");
        if (!SecurityUtils.isMain()) {
            Assert.isTrue(SecurityUtils.getMerchantIds().contains(take.getMerchantId()), "无权操作该盘点单");
        }
        fillList(List.of(take));
        return take;
    }

    @Override
    public void edit(GoodsTakeForm form) {
        GoodsTakeEntity takeEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE.ID.eq(form.getId()))
                .where(GOODS_TAKE.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.notNull(takeEntity, "盘点单不存在");
        Assert.isTrue(SecurityUtils.isMain() || SecurityUtils.getMerchantIds().contains(takeEntity.getMerchantId()), "无权操作该盘点单");
        Assert.isTrue(takeEntity.getStatus().equals(0), "盘点单已结束");
        Assert.isTrue(takeEntity.getTakeMode().equals(1), "pc端只能修改扫码类型盘点单");
        String oldName = takeEntity.getName();
        String remark = takeEntity.getRemark();
        takeEntity.setName(form.getName());
        takeEntity.setRemark(form.getRemark());
        this.mapper.update(takeEntity);
        OpLogUtils.appendOpLog("货品管理-修改盘点单", String.format("""
                盘点单编码: %s,
                盘点单名称: %s 修改为 %s,
                盘点备注: %s 修改为 %s""", takeEntity.getTakeCode(), oldName, takeEntity.getName(), remark, takeEntity.getRemark()), null);
    }

    @Override
    public Page<TakeGoodsPageVo> goodsPage(TakeGoodsPageQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .leftJoin(GOODS).on(GOODS_TAKE_DETAIL.GOODS_ID.eq(GOODS.ID))
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(query.getTakeId()))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        if (!SecurityUtils.isMain()) {
            wrapper.where(GOODS_TAKE_DETAIL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.where(GOODS_TAKE_DETAIL.GOODS_SN.eq(query.getKeyword()).or(GOODS_TAKE_DETAIL.NAME.like(query.getKeyword())));
        }
        if (CollectionUtil.isNotEmpty(query.getIds())) {
            wrapper.where(GOODS_TAKE_DETAIL.ID.in(query.getIds()));
        }
        buildTypeQuery(wrapper, query.getType());
        wrapper.select(
                GOODS_TAKE_DETAIL.ID.as("id"),
                GOODS.ALL_COLUMNS,
                GOODS.ID.as("goodsId"),
                GOODS_TAKE_DETAIL.TOTAL_NUM.as("totalNum"),
                GOODS_TAKE_DETAIL.TAKE_NUM.as("takeNum"),
                GOODS_TAKE_DETAIL.TAKE_BY.as("takeBy"),
                GOODS_TAKE_DETAIL.TAKE_AT.as("takeAt"),
                GOODS_TAKE_DETAIL.STATUS.as("status")
        );
        if (query.getExport().equals(1)) {
            doExportTakeDetail(wrapper);
            return new Page<>();
        }
        if (query.getPrint().equals(1)) {
            long count = goodsTakeDetailMapper.selectCountByQuery(wrapper);
            query.setPrintNum(count);
        }
        Page<TakeGoodsPageVo> page = goodsTakeDetailMapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, TakeGoodsPageVo.class);
        fillTakeDetail(page.getRecords());
        return page;
    }

    @Override
    public GoodsTakeStatisticVo statistic(TakeGoodsPageQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .leftJoin(GOODS).on(GOODS_TAKE_DETAIL.GOODS_ID.eq(GOODS.ID))
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(query.getTakeId()))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_TAKE_DETAIL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        buildTypeQuery(wrapper, query.getType());
        wrapper.select(
                QueryMethods.sum(GOODS_TAKE_DETAIL.TOTAL_NUM).as("totalNum"),
                QueryMethods.sum(GOODS.WEIGHT.multiply(GOODS_TAKE_DETAIL.TOTAL_NUM)).as("totalWeight"),
                QueryMethods.sum(GOODS.NET_GOLD_WEIGHT.multiply(GOODS_TAKE_DETAIL.TOTAL_NUM)).as("totalGoldWeight"),
                QueryMethods.sum(GOODS.NET_SILVER_WEIGHT.multiply(GOODS_TAKE_DETAIL.TOTAL_NUM)).as("totalSilverWeight"),
                QueryMethods.sum(GOODS.COST_PRICE.multiply(GOODS_TAKE_DETAIL.TOTAL_NUM)).as("totalCostPrice")
        );
        GoodsTakeStatisticVo statisticVo = goodsTakeDetailMapper.selectOneByQueryAs(wrapper, GoodsTakeStatisticVo.class);
        if (Objects.isNull(statisticVo)) {
            return new GoodsTakeStatisticVo();
        }
        // 格式化
        statisticVo.setTotalWeight(PriceUtil.formatThreeDecimal(statisticVo.getTotalWeight()));
        statisticVo.setTotalGoldWeight(PriceUtil.formatThreeDecimal(statisticVo.getTotalGoldWeight()));
        statisticVo.setTotalSilverWeight(PriceUtil.formatThreeDecimal(statisticVo.getTotalSilverWeight()));
        statisticVo.setTotalCostPrice(PriceUtil.fen2yuanString(statisticVo.getTotalCostPrice()));
        // 成本价加密
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        statisticVo.setTotalCostPrice(ColumnEncryptUtil.handleEncryptPrice(statisticVo.getTotalCostPrice(), column));
        return statisticVo;
    }

    @Override
    @Transactional
    public void finish(Long takeId) {
        GoodsTakeEntity take = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE.ID.eq(takeId))
                .forUpdate());
        Assert.notNull(take, "盘点单不存在");
        Assert.isTrue(take.getCompanyId().equals(SecurityUtils.getCompanyId())
                && SecurityUtils.getMerchantIds().contains(take.getMerchantId()), "无权操作该盘点单");
        Assert.isTrue(take.getStatus() == 0, "盘点单已结束");

        take.setStatus(1);
        // 未盘点到的详情，全部设为盘亏
        Integer lossNum = goodsTakeDetailMapper.selectOneByQueryAs(QueryWrapper.create()
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(takeId))
                .where(GOODS_TAKE_DETAIL.TAKE_NUM.eq(0))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .select(QueryMethods.sum(GOODS_TAKE_DETAIL.TOTAL_NUM)), Integer.class);
        if (lossNum == null) {
            lossNum = 0;
        }
        take.setLossNum(take.getLossNum() + lossNum);
        boolean result = take.getTotalNum().equals(take.getTakeNum());
        take.setResult(result ? 1 : 0);
        // 更新盘点单
        this.mapper.update(take);
        // 删除rfid盘点缓存
        String key = KEY_TAKE_RFID + SecurityUtils.getCompanyId() + ":" + take.getTakeCode();
        redisTemplate.delete(key);
        // 更新详情状态
        UpdateChain.of(GoodsTakeDetailEntity.class)
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(takeId))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .set(GOODS_TAKE_DETAIL.STATUS, 1)
                .update();
        // 解锁所有货品的盘点状态
        List<Long> goodsIds = goodsTakeDetailMapper.selectListByQueryAs(QueryWrapper.create()
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(takeId))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_TAKE_DETAIL.ABNORMAL.eq(0))
                .select(GOODS_TAKE_DETAIL.GOODS_ID), Long.class);
        UpdateChain.of(GoodsEntity.class)
                .where(GOODS.ID.in(goodsIds))
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.TAKE_STATUS.eq(1))
                .set(GOODS.TAKE_STATUS, 0)
                .update();
        OpLogUtils.appendOpLog("货品管理-结束盘点", String.format("""
                盘点单编码: %s""", take.getTakeCode()), null);
    }

    @Override
    public Page<PdaTakeGoodsPageVo> pdaGoodsPage(TakeGoodsPageQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .leftJoin(GOODS).on(GOODS_TAKE_DETAIL.GOODS_ID.eq(GOODS.ID))
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(query.getTakeId()))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_TAKE_DETAIL.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        buildTypeQuery(wrapper, query.getType());
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.where(GOODS_TAKE_DETAIL.GOODS_SN.eq(query.getKeyword()).or(GOODS_TAKE_DETAIL.NAME.like(query.getKeyword())));
        }
        wrapper.select(
                GOODS_TAKE_DETAIL.ID.as("id"),
                GOODS.ALL_COLUMNS,
                GOODS.ID.as("goodsId")
        );
        Page<PdaTakeGoodsPageVo> page = goodsTakeDetailMapper.paginateAs(query.getPageNum(), query.getPageSize(), wrapper, PdaTakeGoodsPageVo.class);
        Set<Long> goodsIds = page.getRecords().stream().map(PdaTakeGoodsPageVo::getGoodsId).collect(Collectors.toSet());
        Set<Long> counterIds = page.getRecords().stream().map(PdaTakeGoodsPageVo::getCounterId).collect(Collectors.toSet());
        ListFillUtil.of(page.getRecords())
                .build(listFillService::getGoodsImgByGoodsId, goodsIds, "goodsId", "image")
                .build(listFillService::getCounterNameById, counterIds, "counterId", "counter")
                .peek(obj -> {
                    PdaTakeGoodsPageVo vo = (PdaTakeGoodsPageVo) obj;
                    GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.TAG_PRICE.getSign());
                    String tagPrice = ColumnEncryptUtil.handleEncryptPrice(PriceUtil.fen2yuanString(vo.getTagPrice()), column);
                    vo.setTagPrice(tagPrice);
                })
                .handle();
        return page;
    }

    @Override
    @Transactional
    public void reset(Long id) {
        GoodsTakeEntity takeEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE.ID.eq(id))
                .where(GOODS_TAKE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .forUpdate());
        Assert.notNull(takeEntity, "盘点单不存在");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(takeEntity.getMerchantId()), "无权操作该盘点单");
        Assert.isTrue(takeEntity.getStatus().equals(0), "盘点单已结束");
        Assert.isTrue(takeEntity.getTakeMode().equals(2), "只能重置rfid类型盘点单");
        // 删除异常状态明细
        goodsTakeDetailMapper.deleteByQuery(QueryWrapper.create()
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(id))
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_TAKE_DETAIL.ABNORMAL.eq(1)));
        // 修改详情已盘数量
        UpdateChain.of(GoodsTakeDetailEntity.class)
                .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(id))
                .set(GOODS_TAKE_DETAIL.TAKE_NUM, 0)
                .update();
        // 修改盘点单
        takeEntity.setTakeNum(0).setSurplusNum(0).setLossNum(0).setAbnormalNum(0);
        this.mapper.update(takeEntity);
        // 删除rfid盘点缓存
        String key = KEY_TAKE_RFID + SecurityUtils.getCompanyId() + ":" + takeEntity.getTakeCode();
        redisTemplate.delete(key);
    }

    @Override
    @Transactional
    public void rfidTake(RfidTakeForm form) {
        GoodsTakeEntity takeEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_TAKE.ID.eq(form.getId()))
                .where(GOODS_TAKE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .forUpdate());
        Assert.notNull(takeEntity, "盘点单不存在");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(takeEntity.getMerchantId()), "无权操作该盘点单");
        Assert.isTrue(takeEntity.getStatus().equals(0), "盘点单已结束");
        Assert.isTrue(takeEntity.getTakeMode().equals(2), "只能盘点rfid类型盘点单");
        Date current = new Date();
        String lockKey = LOCK_KEY_TAKE_RFID + SecurityUtils.getCompanyId() + ":" + takeEntity.getTakeCode();
        String key = KEY_TAKE_RFID + SecurityUtils.getCompanyId() + ":" + takeEntity.getTakeCode();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock(5, TimeUnit.SECONDS);
            Set<String> rfidList = Optional.ofNullable((Set<String>) redisTemplate.opsForValue().get(key)).orElse(new HashSet<>());
            List<String> newTakeList = form.getRfidList().stream().filter(rfid -> !rfidList.contains(rfid)).toList();
            rfidList.addAll(newTakeList);
            if (newTakeList.isEmpty()) {
                return;
            }
            // 查询本次盘点详情
            List<GoodsTakeDetailEntity> takeDetail = goodsTakeDetailMapper.selectListByQuery(QueryWrapper.create()
                    .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(form.getId()))
                    .where(GOODS_TAKE_DETAIL.RFID.in(newTakeList)));
            // 本次盘点的所有盘点单内货品id
            Set<Long> detailGoodsIds = takeDetail.stream().map(GoodsTakeDetailEntity::getGoodsId).collect(Collectors.toSet());
            // 查询异常盘点货品
            List<GoodsEntity> goodsEntities = goodsMapper.selectListByQuery(QueryWrapper.create()
                    .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(QueryMethods.exists(QueryWrapper.create().from(GOODS_HAS_RFID)
                            .where(GOODS_HAS_RFID.GOODS_ID.eq(GOODS.ID))
                            .where(GOODS_HAS_RFID.RFID.in(newTakeList))))
                    .where(GOODS.ID.notIn(detailGoodsIds)));
            // 保存异常盘点货品
            List<GoodsTakeDetailEntity> list = goodsEntities.stream().map(item -> GoodsTakeDetailEntity.builder()
                    .takeId(takeEntity.getId())
                    .takeAt(current)
                    .takeBy(SecurityUtils.getUserId())
                    .abnormal(1)
                    .totalNum(1)
                    .takeNum(1)
                    .status(0)
                    .companyId(SecurityUtils.getCompanyId())
                    .goodsSn(item.getGoodsSn())
                    .counterId(item.getCounterId().longValue())
                    .goodsId(item.getId())
                    .merchantId(item.getMerchantId())
                    .name(item.getName()).build()).toList();
            Set<Long> goodsIds = list.stream().map(GoodsTakeDetailEntity::getGoodsId).collect(Collectors.toSet());
            ListFillUtil.of(list)
                    .build(listFillService::getRfidByGoodsId, goodsIds,"goodsId", "rfid")
                    .handle();
            goodsTakeDetailMapper.insertBatchSelective(list, 2000);
            // 更新盘点详情
            Set<Long> takeDetailIds = takeDetail.stream().map(GoodsTakeDetailEntity::getId).collect(Collectors.toSet());
            UpdateChain.of(GoodsTakeDetailEntity.class)
                    .where(GOODS_TAKE_DETAIL.ID.in(takeDetailIds))
                    .where(GOODS_TAKE_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(GOODS_TAKE_DETAIL.TAKE_ID.eq(takeEntity.getId()))
                    .set(GOODS_TAKE_DETAIL.TAKE_NUM, GOODS_TAKE_DETAIL.TAKE_NUM.add(1))
                    .set(GOODS_TAKE_DETAIL.TAKE_BY, SecurityUtils.getUserId())
                    .set(GOODS_TAKE_DETAIL.TAKE_AT, current)
                    .update();
            // 更新盘点单, 异常
            takeEntity.setAbnormalNum(takeEntity.getAbnormalNum() + list.size());
            // 本次盘点查询出来，且已经盘点过的货品数, 盘盈
            long surplusNum = takeDetail.stream().filter(item -> item.getTakeNum() > 0).count();
            takeEntity.setSurplusNum(takeEntity.getSurplusNum() + (int) surplusNum);
            // 已盘
            takeEntity.setTakeNum(takeEntity.getTakeNum() + takeDetail.size());
            this.mapper.update(takeEntity);
            // 更新rfid盘点缓存
            redisTemplate.opsForValue().set(key, rfidList);
        }finally {
            lock.unlock();
        }
    }

    private void buildTypeQuery(QueryWrapper wrapper, Integer type) {
        if (type != null) {
            switch (type) {
                case 1:
                    wrapper.where(GOODS_TAKE_DETAIL.TAKE_NUM.gt(0)
                            .and(GOODS_TAKE_DETAIL.ABNORMAL.eq(0)));
                    break;
                case 2:
                    wrapper.where(GOODS_TAKE_DETAIL.TAKE_NUM.eq(0))
                            .and(GOODS_TAKE_DETAIL.ABNORMAL.eq(0));
                    break;
                case 3:
                    wrapper.where(GOODS_TAKE_DETAIL.TAKE_NUM.gt(0)
                            .and(GOODS_TAKE_DETAIL.TAKE_NUM.gt(GOODS_TAKE_DETAIL.TOTAL_NUM))
                            .and(GOODS_TAKE_DETAIL.ABNORMAL.eq(0)));
                    break;
                case 4:
                    wrapper.where(GOODS_TAKE_DETAIL.TAKE_NUM.gt(0)
                            .and(GOODS_TAKE_DETAIL.TAKE_NUM.lt(GOODS_TAKE_DETAIL.TOTAL_NUM))
                            .and(GOODS_TAKE_DETAIL.ABNORMAL.eq(0)));
                    break;
                case 5:
                    wrapper.where(GOODS_TAKE_DETAIL.ABNORMAL.eq(1));
                    break;
            }
        }
    }


    private QueryWrapper buildQuery(GoodsTakePageQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(GOODS_TAKE.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        if (!SecurityUtils.isMain()) {
            wrapper.where(GOODS_TAKE.MERCHANT_ID.in(SecurityUtils.getMerchantIds()));
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.where(GOODS_TAKE.TAKE_CODE.eq(query.getKeyword()));
        }
        // 门店
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            wrapper.where(GOODS_TAKE.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }
        // 盘点单号
        if (StringUtils.isNotBlank(query.getTakeCode())) {
            wrapper.where(GOODS_TAKE.TAKE_CODE.eq(query.getTakeCode()));
        }
        // 盘点名称
        if (StringUtils.isNotBlank(query.getTakeName())) {
            wrapper.where(GOODS_TAKE.NAME.like(query.getTakeName()));
        }
        // 盘点类型
        if (query.getTakeType() != null) {
            wrapper.where(GOODS_TAKE.TYPE.eq(query.getTakeType()));
        }
        // 盘点方式
        if (query.getTakeMode() != null) {
            wrapper.where(GOODS_TAKE.TAKE_MODE.eq(query.getTakeMode()));
        }
        // 盘点状态
        if (query.getTakeStatus() != null && query.getTakeStatus() >= 0) {
            wrapper.where(GOODS_TAKE.STATUS.eq(query.getTakeStatus()));
        }
        // 盘点结果
        if (query.getTakeResult() != null && query.getTakeResult() >= 0) {
            wrapper.where(GOODS_TAKE.RESULT.eq(query.getTakeResult()));
        }
        // 创建时间
        if (query.getTimeRange() != null && query.getTimeRange().length == 2) {
            wrapper.where(GOODS_TAKE.CREATED_AT.between(query.getTimeRange()));
        }
        // 结束时间
        if (query.getFinishTimeRange() != null && query.getFinishTimeRange().length == 2) {
            wrapper.where(GOODS_TAKE.COMPLETE_AT.between(query.getFinishTimeRange()));
        }

        // 柜台多选
        if (StringUtils.isNotBlank(query.getCounterIds())) {
            List<String> counterIdList = Arrays.asList(query.getCounterIds().split(","));
            wrapper.where(QueryMethods.exists(QueryWrapper.create().from(GOODS_TAKE_COUNTER)
                    .where(GOODS_TAKE_COUNTER.TAKE_ID.eq(GOODS_TAKE.ID))
                    .where(GOODS_TAKE_COUNTER.COUNTER_ID.in(counterIdList))));
        }
        // 范围-大类
        if (StringUtils.isNotBlank(query.getCategoryIds())) {
            List<String> categoryIdList = Arrays.asList(query.getCategoryIds().split(","));
            wrapper.where(QueryMethods.exists(QueryWrapper.create().from(GOODS_TAKE_CATEGORY)
                    .where(GOODS_TAKE_CATEGORY.TAKE_ID.eq(GOODS_TAKE.ID))
                    .where(GOODS_TAKE_CATEGORY.CATEGORY_ID.in(categoryIdList))));
        }
        // 范围-小类
        if (StringUtils.isNotBlank(query.getSubclassIds())) {
            List<String> subclassIdList = Arrays.asList(query.getSubclassIds().split(","));
            wrapper.where(QueryMethods.exists(QueryWrapper.create().from(GOODS_TAKE_CATEGORY)
                    .where(GOODS_TAKE_CATEGORY.TAKE_ID.eq(GOODS_TAKE.ID))
                    .where(GOODS_TAKE_CATEGORY.SUBCLASS_ID.in(subclassIdList))));
        }
        // 盘点范围-款式
        if (StringUtils.isNotBlank(query.getStyleIds())) {
            List<String> styleIdList = Arrays.asList(query.getSubclassIds().split(","));
            wrapper.where(QueryMethods.exists(QueryWrapper.create().from(GOODS_TAKE_STYLE)
                    .where(GOODS_TAKE_STYLE.TAKE_ID.eq(GOODS_TAKE.ID))
                    .where(GOODS_TAKE_STYLE.STYLE_ID.in(styleIdList))));
        }
        // 盘点人员
        if (query.getCreatedBy() != null) {
            wrapper.where(GOODS_TAKE.CREATED_BY.eq(query.getCreatedBy()));
        }
        return wrapper;
    }

    private void fillList(List<GoodsTakePageVo> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> merchantIds = new HashSet<>(List.of(0L));
        Set<Long> userIds = new HashSet<>(List.of(0L));
        Set<Long> takeIds = new HashSet<>(List.of(0L));
        list.forEach(item -> {
            merchantIds.add(item.getMerchantId());
            userIds.add(item.getCreatedBy());
            takeIds.add(item.getId());
        });
        ListFillUtil.of(list)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchant")
                .build(listFillService::getUserNameByUserId, userIds, "createdBy", "createdByName")
                .build(listFillService::getCountersByTakeId, takeIds, "id", "counters")
                .build(listFillService::getCategoriesByTakeId, takeIds, "id", "categories")
                .build(listFillService::getStylesById, takeIds, "id", "styles")
                .peek(item -> {
                    GoodsTakePageVo vo = (GoodsTakePageVo) item;
                    vo.setTypeName(getTakeTypeName(vo.getType()));
                    vo.setTakeModeName(getTakeModeName(vo.getTakeMode()));
                    vo.setStatusName(getTakeStatusName(vo.getStatus()));
                    vo.setResultName(getTakeResultName(vo.getResult()));
                    vo.setCounterName(vo.getCounters().stream().map(TakeCounterVo::getName).collect(Collectors.joining(",")));
                    // 盘点范围
                    StrBuilder builder = new StrBuilder();
                    Set<String> categoryNames = new LinkedHashSet<>();
                    Set<String> subClassNames = new LinkedHashSet<>();
                    Set<String> categorySubclassNames = new LinkedHashSet<>();
                    if (CollectionUtil.isEmpty(vo.getCategories())) {
                        vo.setCategories(new ArrayList<>());
                    }
                    List<CategoryNodeVo> categories = vo.getCategories().stream().sorted(Comparator
                            .comparing(CategoryNodeVo::getCategoryId)
                            .thenComparing(o -> o.getSubclassId() == null ? -1 : o.getSubclassId()))
                            .toList();
                    categories.forEach(nodeVo -> {
                        categoryNames.add(nodeVo.getName());
                        categorySubclassNames.add(nodeVo.getName());
                        if (StringUtils.isNotBlank(nodeVo.getSubclassName())) {
                            subClassNames.add(nodeVo.getSubclassName());
                            categorySubclassNames.add(nodeVo.getName() + "-" + nodeVo.getSubclassName());
                            categorySubclassNames.remove(nodeVo.getName());
                        }
                    });
                    builder.append(String.join(",", categorySubclassNames)).append(";");
                    if (CollectionUtil.isNotEmpty(vo.getStyles())) {
                            builder.append("\n").append(String.join(",", vo.getStyles().stream().map(TakeStyleVo::getName).toList())).append(";");
                    }
                    vo.setTackRange(builder.toString());
                    vo.setCategoryNames(categoryNames);
                    vo.setSubclassNames(subClassNames);
                    // 盘点数据
                    TakePageNumVo numVo = vo.getNumVo();
                    if (numVo != null) {
                        vo.setTotalNum(numVo.getTotalNum());
                        vo.setTakeNum(numVo.getTakeNum());
                        vo.setSurplusNum(numVo.getSurplusNum());
                        vo.setLossNum(numVo.getLossNum());
                        vo.setAbnormalNum(numVo.getAbnormalNum());
                    }
                }).handle();
    }

    private void fillTakeGoodsList(List<TakeGoodsVo> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> counterIds = new HashSet<>(List.of(0L));
        Set<Long> categoryIds = new HashSet<>(List.of(0L));
        Set<Long> subclassIds  = new HashSet<>(List.of(0L));
        Set<Long> qualityIds = new HashSet<>(List.of(0L));
        Set<Long> technologyIds = new HashSet<>(List.of(0L));
        Set<Long> goodsIds = new HashSet<>();
        list.forEach(vo -> {
            counterIds.add(vo.getCounterId());
            categoryIds.add(vo.getCategoryId());
            subclassIds.add(vo.getSubclassId());
            qualityIds.add(vo.getQualityId());
            technologyIds.add(vo.getTechnologyId());
            goodsIds.add(vo.getGoodsId());
        });
        ListFillUtil.of(list)
                .build(listFillService::getCounterNameById, counterIds, "counterId", "counter")
                .build(listFillService::getCategoryNameById, categoryIds, "categoryId", "category")
                .build(listFillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(listFillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(listFillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(listFillService::getGoodsImgByGoodsId, goodsIds, "goodsId", "image")
                .peek(obj -> {
                    TakeGoodsVo vo = (TakeGoodsVo) obj;
                    vo.setSalesType(vo.getSalesType().equals("1") ? "按重量" : "按数量");
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                })
                .handle();
    }

    private void fillTakeDetail(List<TakeGoodsPageVo> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> brandIds = new HashSet<>(List.of(0L));
        Set<Long> counterIds = new HashSet<>(List.of(0L));
        Set<Long> supplierIds = new HashSet<>(List.of(0L));
        Set<Long> merchantIds = new HashSet<>(List.of(0L));
        Set<Long> styleIds = new HashSet<>(List.of(0L));
        Set<Long> subclassIds = new HashSet<>(List.of(0L));
        Set<Long> qualityIds = new HashSet<>(List.of(0L));
        Set<Long> technologyIds = new HashSet<>(List.of(0L));
        Set<Long> mainStoneIds = new HashSet<>(List.of(0L));
        Set<Long> subStoneIds = new HashSet<>(List.of(0L));
        Set<Long> userIds = new HashSet<>(List.of(0L));
        Set<Long> goodsIds = new HashSet<>(List.of(0L));

        for (TakeGoodsPageVo vo : list) {
            userIds.add(vo.getTakeBy());
            merchantIds.add(vo.getMerchantId());
            counterIds.add(vo.getCounterId());
            supplierIds.add(vo.getSupplierId());
            subclassIds.add(vo.getSubclassId());
            brandIds.add(vo.getBrandId());
            styleIds.add(vo.getStyleId());
            qualityIds.add(vo.getQualityId());
            technologyIds.add(vo.getTechnologyId());
            mainStoneIds.add(vo.getMainStoneId());
            subStoneIds.add(vo.getSubStoneId());
            goodsIds.add(vo.getGoodsId());
        }
        ListFillUtil.of(list)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchant")
                .build(listFillService::getCounterNameById, counterIds, "counterId", "counter")
                .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplier")
                .build(listFillService::getCategoryNameById, null, "categoryId", "category")
                .build(listFillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(listFillService::getBrandNameById, brandIds, "brandId", "brand")
                .build(listFillService::getStyleNameById, styleIds, "styleId", "style")
                .build(listFillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(listFillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(listFillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStone")
                .build(listFillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStone")
                .build(listFillService::getUserNameByUserId, userIds, "takeBy", "takeByName")
                .build(listFillService::getGoodsImgByGoodsId, goodsIds, "goodsId", "image")
                .build(listFillService::getColumnVosById, goodsIds, "goodsId", "customerColumns")
                .peek(obj -> {
                    TakeGoodsPageVo vo = (TakeGoodsPageVo) obj;
                    int num = vo.getTakeNum() - vo.getTotalNum();
                    vo.setSurplusNum(Math.max(num, 0));
                    // 盘点中 take数量为0时为未盘点到，盘点完成 take数量为0时为盘亏
                    int lossNum = num < 0 ? Math.abs(num) : 0;
                    if (vo.getStatus().equals(0)) {
                        vo.setLossNum(vo.getTakeNum() == 0 ? 0 : lossNum);
                    }else {
                        vo.setLossNum(lossNum);
                    }
                    // 分转元
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));

                    vo.setSalesType("1".equals(vo.getSalesType()) ? "按重量" : "按数量");
                })
                .handle();
    }

    private void doExport(QueryWrapper query) {
        ExcelUtil.of(this.mapper, query, GoodsTakePageVo.class, "goods_take")
                .getData((mapper, queryWrapper) -> {
                    List<GoodsTakePageVo> vos = mapper.selectListByQueryAs(queryWrapper, GoodsTakePageVo.class);
                    fillList(vos);
                    return vos;
                }).doExport();
    }


    private void doExportTakeDetail(QueryWrapper wrapper) {
        ExcelUtil.of(goodsTakeDetailMapper, wrapper, TakeGoodsPageVo.class, "goods_take_detail")
                .getData((mapper, queryWrapper) -> {
                    List<TakeGoodsPageVo> vos = mapper.selectListByQueryAs(queryWrapper, TakeGoodsPageVo.class);
                    fillTakeDetail(vos);
                    return ColumnEncryptUtil.encrypt(vos, TakeGoodsPageVo.class, "customerColumns");
                }).doExport();
    }

    private String getTakeTypeName(Integer type) {
        if (type == null) return "";
        return switch (type) {
            case 1 -> "明盘";
            case 2 -> "盲盘";
            default -> "";
        };
    }
    private String getTakeModeName(Integer mode) {
        if (mode == null) return "";
        return switch (mode) {
            case 1 -> "扫码盘点";
            case 2 -> "rfid盘点";
            default -> "";
        };
    }
    private String getTakeStatusName(Integer status) {
        if (status == null) return "";
        return switch (status) {
            case 0 -> "盘点中";
            case 1 -> "盘点完成";
            case 2 -> "取消盘点";
            default -> "";
        };
    }
    private String getTakeResultName(Integer result) {
        if (result == null) return "";
        return switch (result) {
            case 0 -> "不一致";
            case 1 -> "一致";
            default -> "";
        };
    }
} 