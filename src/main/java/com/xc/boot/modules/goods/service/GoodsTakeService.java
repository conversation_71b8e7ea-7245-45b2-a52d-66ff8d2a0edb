package com.xc.boot.modules.goods.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.goods.model.entity.GoodsTakeEntity;
import com.xc.boot.modules.goods.model.form.GoodsTakeEditForm;
import com.xc.boot.modules.goods.model.form.GoodsTakeForm;
import com.xc.boot.modules.goods.model.query.GoodsTakePageQuery;
import com.xc.boot.modules.goods.model.query.TakeGoodsListQuery;
import com.xc.boot.modules.goods.model.query.TakeGoodsPageQuery;
import com.xc.boot.modules.goods.model.vo.GoodsTakePageVo;
import com.xc.boot.modules.goods.model.vo.GoodsTakeStatisticVo;
import com.xc.boot.modules.goods.model.vo.TakeGoodsPageVo;
import com.xc.boot.modules.goods.model.vo.TakeGoodsVo;
import com.xc.boot.modules.pda.model.form.RfidTakeForm;
import com.xc.boot.modules.pda.model.vo.PdaTakeGoodsPageVo;

public interface GoodsTakeService extends IService<GoodsTakeEntity> {
    /**
     * 盘点单分页查询
     */
    Page<GoodsTakePageVo> pageTake(GoodsTakePageQuery query);

    /**
     * 新增盘点单
     */
    void addTake(GoodsTakeForm form);

    /**
     * 查询盘点货品
     * @param query 查询参数
     * @return 盘点货品
     */
    Page<TakeGoodsVo> goodsList(TakeGoodsListQuery query);

    /**
     * 确认盘点货品
     * @param form 修改表单
     */
    void confirm(GoodsTakeEditForm form);

    /**
     * 修改已盘点数
     * @param form 修改表单
     */
    void editNum(GoodsTakeEditForm form);

    /**
     * 盘点单详情查询
     * @param id 盘点单ID
     * @return 盘点单详情
     */
    GoodsTakePageVo detailTake(Long id);

    /**
     * 编辑盘点单
     * @param form 修改表单
     */
    void edit(GoodsTakeForm form);

    /**
     * 查询盘点货品
     * @param query 查询参数
     * @return 盘点货品
     */
    Page<TakeGoodsPageVo> goodsPage(TakeGoodsPageQuery query);

    /**
     * 盘点货品详情统计
     * @param query 查询参数
     * @return 盘点货品详情统计
     */
    GoodsTakeStatisticVo statistic(TakeGoodsPageQuery query);

    /**
     * 结束盘点
     * @param takeId 盘点单id
     */
    void finish(Long takeId);

    /**
     * PDA盘点货品分页列表
     * @param query 查询参数
     * @return 盘点货品
     */
    Page<PdaTakeGoodsPageVo> pdaGoodsPage(TakeGoodsPageQuery query);

    /**
     * 重置盘点
     * @param id 盘点单id
     */
    void reset(Long id);

    /**
     * RFID盘点
     * @param form 盘点单表单
     */
    void rfidTake(RfidTakeForm form);
}