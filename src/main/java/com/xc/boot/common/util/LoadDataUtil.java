package com.xc.boot.common.util;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.datasource.DataSourceKey;
import com.mybatisflex.core.datasource.FlexDataSource;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Map;

@Component
public class LoadDataUtil implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        LoadDataUtil.applicationContext = applicationContext;
    }

    public static void executeSqlByCurrentDb(String sql) {
        String currentDb = DataSourceKey.get();
        FlexDataSource flexDataSource = FlexGlobalConfig.getDefaultConfig().getDataSource();
        Map<String, DataSource> sourceMap = flexDataSource.getDataSourceMap();
        DataSource dataSource = sourceMap.get(currentDb);
        try {
            Connection connection = dataSource.getConnection();
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.execute();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}
