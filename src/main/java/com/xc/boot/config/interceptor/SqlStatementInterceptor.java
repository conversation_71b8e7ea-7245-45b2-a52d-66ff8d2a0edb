package com.xc.boot.config.interceptor;

import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.datasource.DataSourceKey;
import com.mybatisflex.core.datasource.FlexDataSource;
import com.xc.boot.config.interceptor.model.BaseDbTables;
import com.xc.boot.config.interceptor.model.UserDataSourceDto;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.util.TablesNamesFinder;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.InvocationTargetException;
import java.sql.SQLSyntaxErrorException;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName SqlStatementInterceptor
 * @Date: 2025/5/29 14:20
 * @Description: sql拦截器
 */
@Slf4j
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})
})
@RequiredArgsConstructor
public class SqlStatementInterceptor implements Interceptor {
    private static final String BASE_DB = "base";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        SysUserDetails sysUser = SecurityUtils.getUser().orElse(new SysUserDetails());
        Long companyId = sysUser.getCompanyId();
        try {
            String currentDb = DataSourceKey.get();
            // 获取当前执行表名
            Object[] objects = invocation.getArgs();
            MappedStatement mappedStatement = (MappedStatement) objects[0];
            String sql = mappedStatement.getSqlSource().getBoundSql(objects[1]).getSql();
            Set<String> tableNames = TablesNamesFinder.findTables(sql);
            String tableName = tableNames.isEmpty() ? null : tableNames.iterator().next().replaceAll("`", "").trim().replaceAll("\\s+.*", "");
            if (StringUtils.isBlank(tableName)) {
                log.error("获取当前执行表名失败:{}", sql);
                throw new SQLSyntaxErrorException("获取当前执行表名失败");
            }

            // 如果是基础库表
            if (BaseDbTables.BASE_TABLES.contains(tableName)) {
                setBaseDb(currentDb);
                return invocation.proceed();
            }

            // 业务库表
            if (Objects.isNull(companyId)) {
                setBaseDb(currentDb);
                log.error("当前用户为空或当前用户未绑定商户，切换数据库:{}", BASE_DB);
                return invocation.proceed();
            }

            // 获取当前用户数据源信息
            String config = sysUser.getCompany().getDbConfig();
            UserDataSourceDto dataSourceDto = null;
            // 从登录信息中取得当前用户数据源信息
            try {
                dataSourceDto = JSONUtil.toBean(config, UserDataSourceDto.class);
            } catch (Exception ignored) {
            }
            if (StringUtils.isBlank(config) || Objects.isNull(dataSourceDto)) {
                setBaseDb(currentDb);
                log.error("当前商户未正确配置数据源，切换数据库:{}", BASE_DB);
                return invocation.proceed();
            }

            // 判断是否切换数据源
            String dbName = getDbName(dataSourceDto);
            if (!dbName.equals(currentDb)) {
                log.info("切换到业务数据源{}", dbName);
                DataSourceKey.use(dbName);
            }
            return invocation.proceed();
        }catch (InvocationTargetException exception) {
            throw exception.getTargetException();
        }catch (Exception e) {
            log.error("SqlStatementInterceptor error:{}", e.getMessage());
            throw new SQLSyntaxErrorException(e.getMessage(), e);
        }
    }

    /**
     * 获取数据源名称,如果数据源不存在，则创建数据源
     */
    private String getDbName(UserDataSourceDto userDatasource) {
        String dbName = userDatasource.getDatabase();
        FlexDataSource flexDataSource = FlexGlobalConfig.getDefaultConfig().getDataSource();
        if (!flexDataSource.getDataSourceMap().containsKey(dbName)) {
            HikariDataSource userDataSource = getHikariDataSource(userDatasource);
            //动态添加数据源
            flexDataSource.addDataSource(dbName, userDataSource);
        }
        return dbName;
    }

    @NotNull
    private static HikariDataSource getHikariDataSource(UserDataSourceDto userDatasource) {
        String url = "jdbc:mysql://" + userDatasource.getHost() + ":" + userDatasource.getPort() + "/" + userDatasource.getDatabase() + "?useUnicode=true&zeroDateTimeBehavior=convertToNull&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai&allowLoadLocalInfile=true";
        HikariDataSource userDataSource = new HikariDataSource();
        userDataSource.setJdbcUrl(url);
        userDataSource.setUsername(userDatasource.getUsername());
        userDataSource.setPassword(userDatasource.getPassword());
        return userDataSource;
    }


    private void setBaseDb(String cur) {
        if (StringUtils.isBlank(cur) || !cur.equals(BASE_DB)) {
            log.info("切换到基础数据源{}", BASE_DB);
            DataSourceKey.use(BASE_DB);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Interceptor.super.plugin(target);
    }

    @Override
    public void setProperties(Properties properties) {
        Interceptor.super.setProperties(properties);
    }
}
